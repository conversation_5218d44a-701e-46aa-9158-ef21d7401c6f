def run(self):
    start_time = time.time()
    for step in range(self.configuration.n_steps):
        # 1. Sampling
        sampled_sequences = self._sampling()
        # 2. Scoring
        score_summary = self._scoring(sampled_sequences, step)
        # 3. Updating
        actor_nlls, critic_nlls, augmented_nlls = self._updating(sampled_sequences, score_summary.total_score)
        # 4. Logging
        self._logging(start_time, step, score_summary, actor_nlls, critic_nlls, augmented_nlls)
        # 5. Analysis (NEW) - Add here